<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AVBD 2D Physics Engine</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f0f0f0;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .canvas-container {
            flex: 1;
            position: relative;
            background: white;
            border-right: 2px solid #ddd;
        }

        #physicsCanvas {
            display: block;
            cursor: crosshair;
        }

        .controls {
            width: 300px;
            background: white;
            padding: 20px;
            overflow-y: auto;
            box-shadow: -2px 0 5px rgba(0,0,0,0.1);
        }

        .control-group {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .control-group h3 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 16px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 8px;
        }

        .control-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .control-row:last-child {
            margin-bottom: 0;
        }

        label {
            flex: 1;
            font-size: 14px;
            color: #6c757d;
        }

        input[type="range"] {
            flex: 2;
            margin: 0 10px;
        }

        input[type="number"] {
            width: 60px;
            padding: 4px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 12px;
        }

        select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background: white;
            font-size: 14px;
        }

        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }

        button:hover {
            background: #0056b3;
        }

        button.secondary {
            background: #6c757d;
        }

        button.secondary:hover {
            background: #545b62;
        }

        .button-row {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        .info-panel {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            color: #495057;
            margin-bottom: 15px;
        }

        .help-text {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
            font-style: italic;
        }

        .checkbox-row {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .checkbox-row input[type="checkbox"] {
            margin-right: 8px;
        }

        .stats {
            font-family: monospace;
            font-size: 11px;
            line-height: 1.4;
        }

        .instructions {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            margin-bottom: 15px;
        }

        .instructions strong {
            display: block;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="canvas-container">
            <canvas id="physicsCanvas"></canvas>
        </div>
        
        <div class="controls">
            <div class="instructions">
                <strong>Steuerung:</strong>
                • WASD oder Mittlere Maustaste: Kamera bewegen<br>
                • QE oder Mausrad: Zoom<br>
                • Linke Maustaste: Objekt ziehen<br>
                • Rechte Maustaste: Box erstellen
            </div>

            <div class="control-group">
                <h3>Szene</h3>
                <select id="sceneSelect">
                    <option value="0">Basic Stack</option>
                    <option value="1">Pendulum Chain</option>
                    <option value="2">Bridge</option>
                    <option value="3" selected>Dominos</option>
                    <option value="4">Mixed Constraints</option>
                    <option value="5">Stress Test</option>
                </select>
                <div class="button-row" style="margin-top: 10px;">
                    <button id="resetBtn">Reset</button>
                    <button id="defaultBtn" class="secondary">Standard</button>
                </div>
            </div>

            <div class="control-group">
                <h3>Box Erstellung</h3>
                <div class="control-row">
                    <label>Reibung:</label>
                    <input type="range" id="boxFriction" min="0" max="2" step="0.1" value="0.5">
                    <input type="number" id="boxFrictionValue" min="0" max="2" step="0.1" value="0.5">
                </div>
                <div class="control-row">
                    <label>Breite:</label>
                    <input type="range" id="boxWidth" min="0.1" max="5" step="0.1" value="1">
                    <input type="number" id="boxWidthValue" min="0.1" max="5" step="0.1" value="1">
                </div>
                <div class="control-row">
                    <label>Höhe:</label>
                    <input type="range" id="boxHeight" min="0.1" max="5" step="0.1" value="1">
                    <input type="number" id="boxHeightValue" min="0.1" max="5" step="0.1" value="1">
                </div>
                <div class="control-row">
                    <label>Geschw. X:</label>
                    <input type="range" id="boxVelX" min="-20" max="20" step="1" value="0">
                    <input type="number" id="boxVelXValue" min="-20" max="20" step="1" value="0">
                </div>
                <div class="control-row">
                    <label>Geschw. Y:</label>
                    <input type="range" id="boxVelY" min="-20" max="20" step="1" value="0">
                    <input type="number" id="boxVelYValue" min="-20" max="20" step="1" value="0">
                </div>
            </div>

            <div class="control-group">
                <h3>Physik Parameter</h3>
                <div class="control-row">
                    <label>Schwerkraft:</label>
                    <input type="range" id="gravity" min="-20" max="20" step="0.5" value="-10">
                    <input type="number" id="gravityValue" min="-20" max="20" step="0.5" value="-10">
                </div>
                <div class="control-row">
                    <label>Zeitschritt:</label>
                    <input type="range" id="dt" min="0.001" max="0.1" step="0.001" value="0.0167">
                    <input type="number" id="dtValue" min="0.001" max="0.1" step="0.001" value="0.0167">
                </div>
                <div class="control-row">
                    <label>Iterationen:</label>
                    <input type="range" id="iterations" min="1" max="50" step="1" value="10">
                    <input type="number" id="iterationsValue" min="1" max="50" step="1" value="10">
                </div>
                <div class="control-row">
                    <label>Alpha:</label>
                    <input type="range" id="alpha" min="0" max="1" step="0.01" value="0.99">
                    <input type="number" id="alphaValue" min="0" max="1" step="0.01" value="0.99">
                </div>
                <div class="control-row">
                    <label>Beta:</label>
                    <input type="range" id="beta" min="1000" max="1000000" step="1000" value="100000">
                    <input type="number" id="betaValue" min="1000" max="1000000" step="1000" value="100000">
                </div>
                <div class="control-row">
                    <label>Gamma:</label>
                    <input type="range" id="gamma" min="0" max="1" step="0.01" value="0.99">
                    <input type="number" id="gammaValue" min="0" max="1" step="0.01" value="0.99">
                </div>
            </div>

            <div class="control-group">
                <h3>Anzeige</h3>
                <div class="checkbox-row">
                    <input type="checkbox" id="showContacts" checked>
                    <label>Kontakte anzeigen</label>
                </div>
                <div class="checkbox-row">
                    <input type="checkbox" id="showConstraints" checked>
                    <label>Constraints anzeigen</label>
                </div>
                <div class="checkbox-row">
                    <input type="checkbox" id="showVelocities">
                    <label>Geschwindigkeiten anzeigen</label>
                </div>
                <div class="checkbox-row">
                    <input type="checkbox" id="showAABB">
                    <label>Bounding Boxes anzeigen</label>
                </div>
            </div>

            <div class="control-group">
                <h3>Statistiken</h3>
                <div class="info-panel">
                    <div class="stats" id="stats">
                        Körper: 0<br>
                        Constraints: 0<br>
                        Kinetische Energie: 0.00<br>
                        FPS: 0
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include all JavaScript files -->
    <script src="math.js"></script>
    <script src="rigid.js"></script>
    <script src="forces.js"></script>
    <script src="solver.js"></script>
    <script src="renderer.js"></script>
    <script src="scenes.js"></script>
    <script src="main.js"></script>
</body>
</html>
