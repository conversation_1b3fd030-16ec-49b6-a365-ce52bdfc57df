/**
 * Main application file for AVBD 2D Physics Engine
 * Handles initialization, input, UI, and main loop
 */

// Global variables
let solver;
let renderer;
let canvas;
let running = true;
let dragJoint = null;
let currentScene = 3;

// Box creation parameters
let boxFriction = 0.5;
let boxSize = new Float2(1, 1);
let boxVelocity = new Float2(0, 0);

// Input state
let keys = {};
let mousePos = new Float2(0, 0);
let mouseButtons = { left: false, middle: false, right: false };

// Performance tracking
let lastTime = 0;
let frameCount = 0;
let fps = 0;
let fpsUpdateTime = 0;

// Initialize the application
function init() {
    canvas = document.getElementById('physicsCanvas');
    
    // Set canvas size
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    
    // Initialize physics solver and renderer
    solver = new Solver();
    renderer = new Renderer(canvas);
    
    // Load initial scene
    scenes[currentScene](solver);
    
    // Set up event listeners
    setupEventListeners();
    setupUI();
    
    // Start main loop
    requestAnimationFrame(mainLoop);
}

// Resize canvas to fit container
function resizeCanvas() {
    const container = canvas.parentElement;
    const rect = container.getBoundingClientRect();
    canvas.width = rect.width;
    canvas.height = rect.height;
    canvas.style.width = rect.width + 'px';
    canvas.style.height = rect.height + 'px';

    if (renderer) {
        renderer.setupCanvas();
    }
}

// Set up event listeners
function setupEventListeners() {
    // Keyboard events
    document.addEventListener('keydown', (e) => {
        keys[e.code] = true;
        
        // Prevent default for navigation keys
        if (['KeyW', 'KeyA', 'KeyS', 'KeyD', 'KeyQ', 'KeyE'].includes(e.code)) {
            e.preventDefault();
        }
    });
    
    document.addEventListener('keyup', (e) => {
        keys[e.code] = false;
    });
    
    // Mouse events
    canvas.addEventListener('mousedown', handleMouseDown);
    canvas.addEventListener('mouseup', handleMouseUp);
    canvas.addEventListener('mousemove', handleMouseMove);
    canvas.addEventListener('wheel', handleWheel);
    canvas.addEventListener('contextmenu', (e) => e.preventDefault());
}

// Handle mouse down
function handleMouseDown(e) {
    const rect = canvas.getBoundingClientRect();
    mousePos = new Float2(e.clientX - rect.left, e.clientY - rect.top);
    const worldPos = renderer.screenToWorld(mousePos);
    
    if (e.button === 0) { // Left mouse button
        mouseButtons.left = true;
        
        // Try to pick a body for dragging
        const local = { value: new Float2() };
        const body = solver.pick(worldPos, local);
        
        if (body) {
            dragJoint = new Joint(
                solver, 
                null, 
                body, 
                worldPos, 
                local.value,
                new Float3(1000, 1000, 0)
            );
        }
    } else if (e.button === 1) { // Middle mouse button
        mouseButtons.middle = true;
    } else if (e.button === 2) { // Right mouse button
        mouseButtons.right = true;
        
        // Create a new box
        solver.addBody(
            boxSize.clone(),
            1.0, // density
            boxFriction,
            new Float3(worldPos.x, worldPos.y, 0),
            new Float3(boxVelocity.x, boxVelocity.y, 0)
        );
    }
}

// Handle mouse up
function handleMouseUp(e) {
    if (e.button === 0) {
        mouseButtons.left = false;
        
        if (dragJoint) {
            dragJoint.disable();
            dragJoint = null;
        }
    } else if (e.button === 1) {
        mouseButtons.middle = false;
    } else if (e.button === 2) {
        mouseButtons.right = false;
    }
}

// Handle mouse move
function handleMouseMove(e) {
    const rect = canvas.getBoundingClientRect();
    const newMousePos = new Float2(e.clientX - rect.left, e.clientY - rect.top);
    const mouseDelta = newMousePos.sub(mousePos);
    mousePos = newMousePos;
    
    const worldPos = renderer.screenToWorld(mousePos);
    
    // Update drag joint
    if (dragJoint) {
        dragJoint.rA = worldPos;
    }
    
    // Camera panning with middle mouse
    if (mouseButtons.middle) {
        renderer.moveCamera(-mouseDelta.x, mouseDelta.y);
    }
}

// Handle mouse wheel
function handleWheel(e) {
    e.preventDefault();
    const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
    renderer.zoomCamera(zoomFactor);
}

// Set up UI controls
function setupUI() {
    // Scene selection
    const sceneSelect = document.getElementById('sceneSelect');
    sceneSelect.addEventListener('change', (e) => {
        currentScene = parseInt(e.target.value);
        scenes[currentScene](solver);
    });
    
    // Reset and default buttons
    document.getElementById('resetBtn').addEventListener('click', () => {
        scenes[currentScene](solver);
    });
    
    document.getElementById('defaultBtn').addEventListener('click', () => {
        solver.defaultParams();
        updateUIFromSolver();
    });
    
    // Set up parameter controls
    setupParameterControl('boxFriction', (value) => boxFriction = value);
    setupParameterControl('boxWidth', (value) => boxSize.x = value);
    setupParameterControl('boxHeight', (value) => boxSize.y = value);
    setupParameterControl('boxVelX', (value) => boxVelocity.x = value);
    setupParameterControl('boxVelY', (value) => boxVelocity.y = value);
    
    setupParameterControl('gravity', (value) => solver.gravity = value);
    setupParameterControl('dt', (value) => solver.dt = value);
    setupParameterControl('iterations', (value) => solver.iterations = value);
    setupParameterControl('alpha', (value) => solver.alpha = value);
    setupParameterControl('beta', (value) => solver.beta = value);
    setupParameterControl('gamma', (value) => solver.gamma = value);
    
    // Display options
    document.getElementById('showContacts').addEventListener('change', (e) => {
        renderer.showContacts = e.target.checked;
    });
    
    document.getElementById('showConstraints').addEventListener('change', (e) => {
        renderer.showConstraints = e.target.checked;
    });
    
    document.getElementById('showVelocities').addEventListener('change', (e) => {
        renderer.showVelocities = e.target.checked;
    });
    
    document.getElementById('showAABB').addEventListener('change', (e) => {
        renderer.showAABB = e.target.checked;
    });
}

// Set up a parameter control (slider + number input)
function setupParameterControl(id, callback) {
    const slider = document.getElementById(id);
    const numberInput = document.getElementById(id + 'Value');
    
    slider.addEventListener('input', (e) => {
        const value = parseFloat(e.target.value);
        numberInput.value = value;
        callback(value);
    });
    
    numberInput.addEventListener('input', (e) => {
        const value = parseFloat(e.target.value);
        slider.value = value;
        callback(value);
    });
}

// Update UI controls from solver parameters
function updateUIFromSolver() {
    document.getElementById('gravity').value = solver.gravity;
    document.getElementById('gravityValue').value = solver.gravity;
    document.getElementById('dt').value = solver.dt;
    document.getElementById('dtValue').value = solver.dt;
    document.getElementById('iterations').value = solver.iterations;
    document.getElementById('iterationsValue').value = solver.iterations;
    document.getElementById('alpha').value = solver.alpha;
    document.getElementById('alphaValue').value = solver.alpha;
    document.getElementById('beta').value = solver.beta;
    document.getElementById('betaValue').value = solver.beta;
    document.getElementById('gamma').value = solver.gamma;
    document.getElementById('gammaValue').value = solver.gamma;
}

// Handle input
function handleInput() {
    const moveSpeed = 10 / renderer.camZoom;
    const zoomSpeed = 1.025;
    
    // Camera movement
    if (keys['KeyD']) renderer.moveCamera(moveSpeed, 0);
    if (keys['KeyA']) renderer.moveCamera(-moveSpeed, 0);
    if (keys['KeyW']) renderer.moveCamera(0, moveSpeed);
    if (keys['KeyS']) renderer.moveCamera(0, -moveSpeed);
    
    // Camera zoom
    if (keys['KeyE']) renderer.zoomCamera(zoomSpeed);
    if (keys['KeyQ']) renderer.zoomCamera(1 / zoomSpeed);
}

// Update statistics display
function updateStats() {
    const bodyCount = solver.getBodyCount();
    const constraintCount = solver.getConstraintCount();
    const kineticEnergy = solver.getTotalKineticEnergy();
    
    const statsElement = document.getElementById('stats');
    statsElement.innerHTML = `
        Körper: ${bodyCount}<br>
        Constraints: ${constraintCount}<br>
        Kinetische Energie: ${kineticEnergy.toFixed(2)}<br>
        FPS: ${fps}
    `;
}

// Main loop
function mainLoop(currentTime) {
    if (!running) return;
    
    // Calculate delta time
    const deltaTime = (currentTime - lastTime) / 1000;
    lastTime = currentTime;
    
    // Update FPS counter
    frameCount++;
    fpsUpdateTime += deltaTime;
    if (fpsUpdateTime >= 1.0) {
        fps = Math.round(frameCount / fpsUpdateTime);
        frameCount = 0;
        fpsUpdateTime = 0;
    }
    
    // Handle input
    handleInput();
    
    // Step physics
    solver.step();
    
    // Render
    renderer.render(solver);
    
    // Update UI
    updateStats();
    
    // Continue loop
    requestAnimationFrame(mainLoop);
}

// Start the application when page loads
document.addEventListener('DOMContentLoaded', init);
