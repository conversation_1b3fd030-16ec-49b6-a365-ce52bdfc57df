/**
 * Demo Scenes for AVBD 2D Physics Engine
 * Various scenarios to demonstrate the engine's capabilities
 */

// Scene names for UI
const sceneNames = [
    "Basic Stack",
    "Pendulum Chain",
    "Bridge",
    "Dominos",
    "Mixed Constraints",
    "Stress Test"
];

// Scene functions
const scenes = [];

// Scene 0: Basic Stack
scenes[0] = function(solver) {
    solver.clear();
    
    // Ground
    const ground = solver.addBody(new Float2(20, 1), 0, 0.5, new Float3(0, -2, 0));
    ground.setStatic();
    
    // Stack of boxes
    for (let i = 0; i < 5; i++) {
        solver.addBody(
            new Float2(1, 1), 
            1.0, 
            0.5, 
            new Float3(0, i * 1.1 + 1, 0)
        );
    }
    
    // Add some side boxes for interaction
    solver.addBody(new Float2(0.8, 0.8), 1.0, 0.3, new Float3(-3, 2, 0));
    solver.addBody(new Float2(0.8, 0.8), 1.0, 0.3, new Float3(3, 2, 0));
};

// Scene 1: Pendulum Chain
scenes[1] = function(solver) {
    solver.clear();
    
    // Anchor point (static)
    const anchor = solver.addBody(new Float2(0.2, 0.2), 0, 0.5, new Float3(0, 8, 0));
    anchor.setStatic();
    
    let prevBody = anchor;
    const chainLength = 6;
    
    // Create chain
    for (let i = 0; i < chainLength; i++) {
        const body = solver.addBody(
            new Float2(0.3, 1.0), 
            1.0, 
            0.2, 
            new Float3(i * 0.5, 7 - i * 1.2, 0)
        );
        
        // Connect with joint
        solver.addJoint(
            prevBody, 
            body, 
            new Float2(0, -0.5), 
            new Float2(0, 0.5),
            new Float3(Infinity, Infinity, Infinity)
        );
        
        prevBody = body;
    }
    
    // Add a heavy weight at the end
    const weight = solver.addBody(new Float2(1, 0.5), 3.0, 0.5, new Float3(chainLength * 0.5, 1, 0));
    solver.addJoint(
        prevBody, 
        weight, 
        new Float2(0, -0.5), 
        new Float2(0, 0.25),
        new Float3(Infinity, Infinity, Infinity)
    );
};

// Scene 2: Bridge
scenes[2] = function(solver) {
    solver.clear();
    
    // Ground
    const ground = solver.addBody(new Float2(20, 1), 0, 0.5, new Float3(0, -2, 0));
    ground.setStatic();
    
    // Bridge supports
    const leftSupport = solver.addBody(new Float2(0.5, 4), 0, 0.5, new Float3(-6, 0, 0));
    leftSupport.setStatic();
    
    const rightSupport = solver.addBody(new Float2(0.5, 4), 0, 0.5, new Float3(6, 0, 0));
    rightSupport.setStatic();
    
    // Bridge planks
    const plankCount = 12;
    const plankWidth = 1.0;
    const bridgeWidth = plankCount * plankWidth;
    const startX = -bridgeWidth / 2 + plankWidth / 2;
    
    let prevPlank = null;
    
    for (let i = 0; i < plankCount; i++) {
        const plank = solver.addBody(
            new Float2(plankWidth, 0.2), 
            1.0, 
            0.3, 
            new Float3(startX + i * plankWidth, 2, 0)
        );
        
        // Connect planks with joints
        if (prevPlank) {
            solver.addJoint(
                prevPlank, 
                plank, 
                new Float2(plankWidth / 2, 0), 
                new Float2(-plankWidth / 2, 0),
                new Float3(Infinity, Infinity, 100) // Flexible rotation
            );
        } else {
            // Connect first plank to left support
            solver.addJoint(
                leftSupport, 
                plank, 
                new Float2(0, 2), 
                new Float2(-plankWidth / 2, 0),
                new Float3(Infinity, Infinity, 100)
            );
        }
        
        // Connect last plank to right support
        if (i === plankCount - 1) {
            solver.addJoint(
                plank, 
                rightSupport, 
                new Float2(plankWidth / 2, 0), 
                new Float2(0, 2),
                new Float3(Infinity, Infinity, 100)
            );
        }
        
        prevPlank = plank;
    }
    
    // Add some test objects
    solver.addBody(new Float2(0.8, 0.8), 2.0, 0.5, new Float3(-2, 4, 0));
    solver.addBody(new Float2(0.8, 0.8), 2.0, 0.5, new Float3(2, 4, 0));
};

// Scene 3: Dominos
scenes[3] = function(solver) {
    solver.clear();
    
    // Ground
    const ground = solver.addBody(new Float2(20, 1), 0, 0.5, new Float3(0, -2, 0));
    ground.setStatic();
    
    // Create domino line
    const dominoCount = 15;
    const dominoSpacing = 0.8;
    const startX = -(dominoCount - 1) * dominoSpacing / 2;
    
    for (let i = 0; i < dominoCount; i++) {
        solver.addBody(
            new Float2(0.2, 2.0), 
            1.0, 
            0.1, 
            new Float3(startX + i * dominoSpacing, 0, 0)
        );
    }
    
    // Add a ball to start the chain reaction
    const ball = solver.addBody(new Float2(0.5, 0.5), 2.0, 0.3, new Float3(-8, 3, 0));
    ball.velocity = new Float3(5, 0, 0);
    
    // Add some obstacles
    solver.addBody(new Float2(1, 0.3), 0, 0.5, new Float3(3, -1, 0)).setStatic();
    solver.addBody(new Float2(0.3, 1), 0, 0.5, new Float3(-3, -1, 0)).setStatic();
};

// Scene 4: Mixed Constraints
scenes[4] = function(solver) {
    solver.clear();
    
    // Ground
    const ground = solver.addBody(new Float2(20, 1), 0, 0.5, new Float3(0, -3, 0));
    ground.setStatic();
    
    // Spring system
    const anchor1 = solver.addBody(new Float2(0.2, 0.2), 0, 0.5, new Float3(-5, 5, 0));
    anchor1.setStatic();
    
    const mass1 = solver.addBody(new Float2(0.8, 0.8), 2.0, 0.3, new Float3(-5, 2, 0));
    solver.addSpring(anchor1, mass1, new Float2(0, 0), new Float2(0, 0), 500, 2.0);
    
    // Joint system
    const anchor2 = solver.addBody(new Float2(0.2, 0.2), 0, 0.5, new Float3(0, 5, 0));
    anchor2.setStatic();
    
    const arm1 = solver.addBody(new Float2(2, 0.3), 1.0, 0.3, new Float3(1, 4, 0));
    solver.addJoint(anchor2, arm1, new Float2(0, 0), new Float2(-1, 0));
    
    const arm2 = solver.addBody(new Float2(1.5, 0.3), 1.0, 0.3, new Float3(2.5, 2.5, Math.PI/4));
    solver.addJoint(arm1, arm2, new Float2(1, 0), new Float2(-0.75, 0));
    
    // Mixed system with spring and joint
    const base = solver.addBody(new Float2(1, 0.5), 0, 0.5, new Float3(5, -2, 0));
    base.setStatic();
    
    const middle = solver.addBody(new Float2(0.6, 0.6), 1.5, 0.3, new Float3(5, 0, 0));
    solver.addSpring(base, middle, new Float2(0, 0.25), new Float2(0, 0), 300, 1.5);
    
    const top = solver.addBody(new Float2(0.4, 0.4), 1.0, 0.3, new Float3(5, 2, 0));
    solver.addJoint(middle, top, new Float2(0, 0.3), new Float2(0, -0.2));
    
    // Add some free bodies for interaction
    solver.addBody(new Float2(0.6, 0.6), 1.0, 0.4, new Float3(-2, 1, 0));
    solver.addBody(new Float2(0.6, 0.6), 1.0, 0.4, new Float3(2, 1, 0));
};

// Scene 5: Stress Test
scenes[5] = function(solver) {
    solver.clear();
    
    // Ground
    const ground = solver.addBody(new Float2(30, 2), 0, 0.5, new Float3(0, -4, 0));
    ground.setStatic();
    
    // Create multiple stacks
    const stackCount = 5;
    const stackHeight = 8;
    
    for (let s = 0; s < stackCount; s++) {
        const stackX = (s - stackCount / 2 + 0.5) * 4;
        
        for (let i = 0; i < stackHeight; i++) {
            const size = new Float2(0.8 + Math.random() * 0.4, 0.8 + Math.random() * 0.4);
            const body = solver.addBody(
                size, 
                1.0, 
                0.3 + Math.random() * 0.4, 
                new Float3(stackX + (Math.random() - 0.5) * 0.2, i * 1.1 - 2, Math.random() * 0.2)
            );
        }
    }
    
    // Add some dynamic obstacles
    for (let i = 0; i < 10; i++) {
        const size = new Float2(0.5 + Math.random() * 0.5, 0.5 + Math.random() * 0.5);
        solver.addBody(
            size, 
            1.0 + Math.random(), 
            0.2 + Math.random() * 0.6, 
            new Float3((Math.random() - 0.5) * 20, 5 + Math.random() * 5, Math.random() * Math.PI)
        );
    }
    
    // Add some connected structures
    for (let c = 0; c < 3; c++) {
        const baseX = (c - 1) * 8;
        const base = solver.addBody(new Float2(0.3, 0.3), 0, 0.5, new Float3(baseX, 3, 0));
        base.setStatic();
        
        let prev = base;
        for (let i = 0; i < 4; i++) {
            const body = solver.addBody(
                new Float2(0.4, 1.2), 
                1.0, 
                0.3, 
                new Float3(baseX + i * 0.6, 2 - i * 1.5, 0)
            );
            
            solver.addJoint(prev, body, new Float2(0, -0.15), new Float2(0, 0.6));
            prev = body;
        }
    }
};

const sceneCount = scenes.length;

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { scenes, sceneNames, sceneCount };
}
