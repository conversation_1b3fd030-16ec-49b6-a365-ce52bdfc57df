# AVBD 2D Physics Engine

Eine JavaScript-Implementierung der Augmented Vertex Block Descent (AVBD) 2D-Physik-Engine, basierend auf dem C++ Repository von [savant117/avbd-demo2d](https://github.com/savant117/avbd-demo2d).

## Über AVBD

Augmented Vertex Block Descent (AVBD) ist eine schnelle, physikbasierte Simulationsmethode, die:
- Unbedingt stabil ist
- Hochgradig parallelisierbar ist
- Zur impliziten Euler-Lösung konvergieren kann
- Harte Constraints mit unendlicher Steifigkeit handhaben kann
- Komplexe Kontaktszenarien mit Reibung unterstützt

## Features

- **Rigid Body Dynamics**: Vollständige 2D-Starrkörpersimulation mit Rotation
- **Constraint System**: Joints, Springs und Kollisionskontakte
- **AVBD Solver**: Implementierung des Augmented Vertex Block Descent Algorithmus
- **Interaktive Demo**: Canvas-basierte Visualisierung mit Maus- und Tastatursteuerung
- **Verschiedene Szenen**: Demonstrationen verschiedener Physikkonzepte
- **Echtzeitparameter**: Anpassbare Physikparameter während der Laufzeit

## Dateien

- `math.js` - Mathematische Grundstrukturen (Float2, Float3, Matrizen)
- `rigid.js` - Rigid Body Klasse mit physikalischen Eigenschaften
- `forces.js` - Force/Constraint System (Joint, Spring, Manifold, IgnoreCollision)
- `solver.js` - Haupt-AVBD-Solver mit Kollisionserkennung
- `renderer.js` - Canvas-basiertes Rendering-System
- `scenes.js` - Demo-Szenen zur Demonstration der Engine-Funktionalität
- `main.js` - Hauptanwendung mit Input-Handling und UI
- `index.html` - HTML-Interface mit Benutzeroberfläche

## Verwendung

1. Öffnen Sie `index.html` in einem modernen Webbrowser
2. Verwenden Sie die Steuerung:
   - **WASD** oder **Mittlere Maustaste**: Kamera bewegen
   - **QE** oder **Mausrad**: Zoom
   - **Linke Maustaste**: Objekt ziehen
   - **Rechte Maustaste**: Box erstellen

## Szenen

1. **Basic Stack**: Einfacher Stapel von Boxen
2. **Pendulum Chain**: Pendelkette mit Joints
3. **Bridge**: Flexible Brücke aus verbundenen Planken
4. **Dominos**: Dominosteine für Kettenreaktion
5. **Mixed Constraints**: Kombination aus Springs und Joints
6. **Stress Test**: Viele Objekte für Performance-Test

## Parameter

### Physik-Parameter
- **Schwerkraft**: Gravitationskraft (-20 bis 20)
- **Zeitschritt**: Simulationszeitschritt (0.001 bis 0.1)
- **Iterationen**: Solver-Iterationen (1 bis 50)
- **Alpha**: Stabilisierungsparameter (0 bis 1)
- **Beta**: Penalty-Ramping-Parameter (1000 bis 1000000)
- **Gamma**: Warmstarting-Decay-Parameter (0 bis 1)

### Box-Erstellung
- **Reibung**: Reibungskoeffizient (0 bis 2)
- **Größe**: Breite und Höhe der Box (0.1 bis 5)
- **Geschwindigkeit**: Anfangsgeschwindigkeit in X/Y-Richtung

### Anzeige-Optionen
- **Kontakte anzeigen**: Zeigt Kollisionskontakte
- **Constraints anzeigen**: Zeigt Joints und Springs
- **Geschwindigkeiten anzeigen**: Zeigt Geschwindigkeitsvektoren
- **Bounding Boxes anzeigen**: Zeigt Kollisions-Bounding-Boxes

## Technische Details

### AVBD-Algorithmus
Der Solver implementiert den AVBD-Algorithmus mit:
- Primal-Dual Updates
- Penalty-Parameter-Ramping
- Warmstarting für bessere Konvergenz
- Geometrische Steifigkeitsbehandlung

### Kollisionserkennung
- Broadphase: Naive O(n²) mit Radius-Check
- Narrowphase: Separating Axis Theorem (SAT) für Box-Box-Kollisionen
- Kontakt-Manifolds mit Reibung

### Constraints
- **Joint**: Revolute Joint mit optionaler Winkelconstraint
- **Spring**: Federkraft zwischen zwei Punkten
- **Manifold**: Kollisionskontakte mit Reibung
- **IgnoreCollision**: Verhindert Kollisionserkennung zwischen Körpern

## Performance

Die Engine ist für Echtzeit-Performance optimiert und kann:
- Hunderte von Rigid Bodies simulieren
- Komplexe Constraint-Netzwerke handhaben
- Stabile Simulation bei niedrigen Iterationszahlen

## Browser-Kompatibilität

Getestet mit:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Lizenz

Basierend auf der MIT-Lizenz des ursprünglichen C++ Repositories.

## Referenzen

- [AVBD Paper](https://graphics.cs.utah.edu/research/projects/avbd/)
- [Original C++ Implementation](https://github.com/savant117/avbd-demo2d)
- [Utah Graphics Lab](https://graphics.cs.utah.edu/)

## Entwicklung

Diese JavaScript-Implementierung wurde entwickelt, um die AVBD-Methode zugänglicher zu machen und Experimente im Browser zu ermöglichen. Sie behält die Kernalgorithmen der ursprünglichen C++-Implementierung bei, während sie eine benutzerfreundliche Web-Interface bietet.
