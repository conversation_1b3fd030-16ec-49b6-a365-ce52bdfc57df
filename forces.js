/**
 * Force/Constraint System for AVBD 2D Physics Engine
 * Based on the C++ implementation from https://github.com/savant117/avbd-demo2d
 */

// Constants
const MAX_ROWS = 4;
const PENALTY_MIN = 1000.0;
const PENALTY_MAX = 1000000000.0;
const COLLISION_MARGIN = 0.01;
const STICK_THRESH = 0.01;
const SHOW_CONTACTS = true;

// Base Force class - provides common interface for all constraints
class Force {
    constructor(solver, bodyA, bodyB) {
        this.solver = solver;
        this.bodyA = bodyA;
        this.bodyB = bodyB;
        this.nextA = null;
        this.nextB = null;
        this.next = null;

        // Constraint data arrays
        this.J = new Array(MAX_ROWS).fill(null).map(() => new Float3());
        this.H = new Array(MAX_ROWS).fill(null).map(() => new Float3x3());
        this.C = new Array(MAX_ROWS).fill(0);
        this.fmin = new Array(MAX_ROWS).fill(-Infinity);
        this.fmax = new Array(MAX_ROWS).fill(Infinity);
        this.stiffness = new Array(MAX_ROWS).fill(Infinity);
        this.motor = new Array(MAX_ROWS).fill(0);
        this.fracture = new Array(MAX_ROWS).fill(Infinity);
        this.penalty = new Array(MAX_ROWS).fill(PENALTY_MIN);
        this.lambda = new Array(MAX_ROWS).fill(0);

        // Add to solver's force list
        this.next = solver.forces;
        solver.forces = this;

        // Add to bodies' force lists
        if (bodyA) {
            this.nextA = bodyA.forces;
            bodyA.forces = this;
        }
        if (bodyB) {
            this.nextB = bodyB.forces;
            bodyB.forces = this;
        }
    }

    disable() {
        // Remove from solver's force list
        if (this.solver.forces === this) {
            this.solver.forces = this.next;
        } else {
            let current = this.solver.forces;
            while (current && current.next !== this) {
                current = current.next;
            }
            if (current) {
                current.next = this.next;
            }
        }

        // Remove from bodyA's force list
        if (this.bodyA) {
            if (this.bodyA.forces === this) {
                this.bodyA.forces = this.nextA;
            } else {
                let current = this.bodyA.forces;
                while (current && current.nextA !== this && current.nextB !== this) {
                    current = (current.bodyA === this.bodyA) ? current.nextA : current.nextB;
                }
                if (current) {
                    if (current.nextA === this) current.nextA = this.nextA;
                    if (current.nextB === this) current.nextB = this.nextA;
                }
            }
        }

        // Remove from bodyB's force list
        if (this.bodyB) {
            if (this.bodyB.forces === this) {
                this.bodyB.forces = this.nextB;
            } else {
                let current = this.bodyB.forces;
                while (current && current.nextA !== this && current.nextB !== this) {
                    current = (current.bodyA === this.bodyB) ? current.nextA : current.nextB;
                }
                if (current) {
                    if (current.nextA === this) current.nextA = this.nextB;
                    if (current.nextB === this) current.nextB = this.nextB;
                }
            }
        }
    }

    // Virtual methods to be overridden by subclasses
    rows() { return 0; }
    initialize() { return true; }
    computeConstraint(alpha) {}
    computeDerivatives(body) {}
    draw(ctx) {}
}

// Joint constraint - revolute joint with optional angle constraint
class Joint extends Force {
    constructor(solver, bodyA, bodyB, rA, rB, stiffness = new Float3(Infinity, Infinity, Infinity), motor = 0.0, fracture = Infinity) {
        super(solver, bodyA, bodyB);
        
        this.rA = rA.clone();
        this.rB = rB.clone();
        this.C0 = new Float3();
        this.torqueArm = 0;
        this.restAngle = 0;

        // Set constraint parameters
        for (let i = 0; i < 3; i++) {
            this.stiffness[i] = stiffness.get(i);
            this.fracture[i] = fracture;
        }
        this.motor[2] = motor;

        // Calculate rest angle if both bodies exist
        if (bodyA && bodyB) {
            this.restAngle = bodyB.position.z - bodyA.position.z;
        }
    }

    rows() { return 3; }

    initialize() {
        return true;
    }

    computeConstraint(alpha) {
        if (!this.bodyA || !this.bodyB) return;

        // Position constraint
        const pA = this.bodyA.position.xy().add(rotation(this.bodyA.position.z).mulVec(this.rA));
        const pB = this.bodyB.position.xy().add(rotation(this.bodyB.position.z).mulVec(this.rB));
        const dp = pB.sub(pA);

        this.C[0] = dp.x;
        this.C[1] = dp.y;

        // Angle constraint
        const dAngle = this.bodyB.position.z - this.bodyA.position.z - this.restAngle;
        this.C[2] = dAngle;

        // Apply stabilization
        this.C[0] *= alpha;
        this.C[1] *= alpha;
        this.C[2] *= alpha;
    }

    computeDerivatives(body) {
        if (body === this.bodyA) {
            // Jacobian for body A
            const rA_rot = rotation(this.bodyA.position.z).mulVec(this.rA);
            this.J[0] = new Float3(-1, 0, rA_rot.y);
            this.J[1] = new Float3(0, -1, -rA_rot.x);
            this.J[2] = new Float3(0, 0, -1);
        } else if (body === this.bodyB) {
            // Jacobian for body B
            const rB_rot = rotation(this.bodyB.position.z).mulVec(this.rB);
            this.J[0] = new Float3(1, 0, -rB_rot.y);
            this.J[1] = new Float3(0, 1, rB_rot.x);
            this.J[2] = new Float3(0, 0, 1);
        }

        // Hessian is zero for linear constraints
        for (let i = 0; i < 3; i++) {
            this.H[i] = new Float3x3();
        }
    }

    draw(ctx) {
        if (!ctx || !this.bodyA || !this.bodyB) return;

        const pA = this.bodyA.position.xy().add(rotation(this.bodyA.position.z).mulVec(this.rA));
        const pB = this.bodyB.position.xy().add(rotation(this.bodyB.position.z).mulVec(this.rB));

        ctx.save();
        ctx.strokeStyle = '#ff0000';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(pA.x, pA.y, 3, 0, 2 * Math.PI);
        ctx.stroke();
        ctx.beginPath();
        ctx.arc(pB.x, pB.y, 3, 0, 2 * Math.PI);
        ctx.stroke();
        ctx.restore();
    }
}

// Spring force
class Spring extends Force {
    constructor(solver, bodyA, bodyB, rA, rB, stiffness, rest = -1) {
        super(solver, bodyA, bodyB);
        
        this.rA = rA.clone();
        this.rB = rB.clone();
        this.stiffness[0] = stiffness;

        // Calculate rest length if not provided
        if (rest < 0 && bodyA && bodyB) {
            const pA = bodyA.position.xy().add(rotation(bodyA.position.z).mulVec(rA));
            const pB = bodyB.position.xy().add(rotation(bodyB.position.z).mulVec(rB));
            this.rest = length(pB.sub(pA));
        } else {
            this.rest = rest;
        }
    }

    rows() { return 1; }

    initialize() { return true; }

    computeConstraint(alpha) {
        if (!this.bodyA || !this.bodyB) return;

        const pA = this.bodyA.position.xy().add(rotation(this.bodyA.position.z).mulVec(this.rA));
        const pB = this.bodyB.position.xy().add(rotation(this.bodyB.position.z).mulVec(this.rB));
        const dp = pB.sub(pA);
        const currentLength = length(dp);

        this.C[0] = (currentLength - this.rest) * alpha;
    }

    computeDerivatives(body) {
        if (!this.bodyA || !this.bodyB) return;

        const pA = this.bodyA.position.xy().add(rotation(this.bodyA.position.z).mulVec(this.rA));
        const pB = this.bodyB.position.xy().add(rotation(this.bodyB.position.z).mulVec(this.rB));
        const dp = pB.sub(pA);
        const currentLength = length(dp);
        
        if (currentLength < 1e-6) return; // Avoid division by zero

        const n = dp.div(currentLength);

        if (body === this.bodyA) {
            const rA_rot = rotation(this.bodyA.position.z).mulVec(this.rA);
            this.J[0] = new Float3(-n.x, -n.y, cross(rA_rot, n.neg()));
        } else if (body === this.bodyB) {
            const rB_rot = rotation(this.bodyB.position.z).mulVec(this.rB);
            this.J[0] = new Float3(n.x, n.y, cross(rB_rot, n));
        }

        // Hessian is zero for this simple spring
        this.H[0] = new Float3x3();
    }

    draw(ctx) {
        if (!ctx || !this.bodyA || !this.bodyB) return;

        const pA = this.bodyA.position.xy().add(rotation(this.bodyA.position.z).mulVec(this.rA));
        const pB = this.bodyB.position.xy().add(rotation(this.bodyB.position.z).mulVec(this.rB));

        ctx.save();
        ctx.strokeStyle = '#00ff00';
        ctx.lineWidth = 1;
        ctx.setLineDash([5, 5]);
        ctx.beginPath();
        ctx.moveTo(pA.x, pA.y);
        ctx.lineTo(pB.x, pB.y);
        ctx.stroke();
        ctx.restore();
    }
}

// Ignore collision force - prevents collision detection between two bodies
class IgnoreCollision extends Force {
    constructor(solver, bodyA, bodyB) {
        super(solver, bodyA, bodyB);
    }

    rows() { return 0; }
    initialize() { return true; }
    computeConstraint(alpha) {}
    computeDerivatives(body) {}
    draw(ctx) {}
}

// Collision manifold between two rigid bodies with frictional contact points
class Manifold extends Force {
    constructor(solver, bodyA, bodyB) {
        super(solver, bodyA, bodyB);

        this.contacts = [];
        this.numContacts = 0;
        this.friction = Math.sqrt(bodyA.friction * bodyB.friction);

        // Generate contact points
        this.numContacts = Manifold.collide(bodyA, bodyB, this.contacts);

        // Set up friction bounds for contact constraints
        for (let i = 0; i < this.numContacts; i++) {
            const baseIdx = i * 2;
            // Normal force (non-penetration)
            this.fmin[baseIdx] = 0; // No pulling
            this.fmax[baseIdx] = Infinity;
            // Tangent force (friction)
            this.fmin[baseIdx + 1] = -Infinity;
            this.fmax[baseIdx + 1] = Infinity;
        }
    }

    rows() {
        return this.numContacts * 2;
    }

    initialize() {
        // Remove manifold if no contacts
        return this.numContacts > 0;
    }

    computeConstraint(alpha) {
        for (let i = 0; i < this.numContacts; i++) {
            const contact = this.contacts[i];
            const baseIdx = i * 2;

            // Get contact points in world space
            const pA = this.bodyA.position.xy().add(rotation(this.bodyA.position.z).mulVec(contact.rA));
            const pB = this.bodyB.position.xy().add(rotation(this.bodyB.position.z).mulVec(contact.rB));

            // Penetration depth (normal constraint)
            const dp = pB.sub(pA);
            this.C[baseIdx] = dot(dp, contact.normal) * alpha;

            // Tangential constraint (friction)
            const tangent = new Float2(-contact.normal.y, contact.normal.x);
            this.C[baseIdx + 1] = dot(dp, tangent) * alpha;

            // Update friction bounds based on normal force
            const normalForce = Math.abs(this.lambda[baseIdx]);
            const maxFriction = this.friction * normalForce;
            this.fmin[baseIdx + 1] = -maxFriction;
            this.fmax[baseIdx + 1] = maxFriction;

            // Check for sticking contact
            contact.stick = Math.abs(this.C[baseIdx + 1]) < STICK_THRESH;
        }
    }

    computeDerivatives(body) {
        for (let i = 0; i < this.numContacts; i++) {
            const contact = this.contacts[i];
            const baseIdx = i * 2;

            if (body === this.bodyA) {
                // Jacobian for body A
                const rA_rot = rotation(this.bodyA.position.z).mulVec(contact.rA);

                // Normal constraint
                this.J[baseIdx] = new Float3(
                    -contact.normal.x,
                    -contact.normal.y,
                    cross(rA_rot, contact.normal.neg())
                );

                // Tangent constraint
                const tangent = new Float2(-contact.normal.y, contact.normal.x);
                this.J[baseIdx + 1] = new Float3(
                    -tangent.x,
                    -tangent.y,
                    cross(rA_rot, tangent.neg())
                );

            } else if (body === this.bodyB) {
                // Jacobian for body B
                const rB_rot = rotation(this.bodyB.position.z).mulVec(contact.rB);

                // Normal constraint
                this.J[baseIdx] = new Float3(
                    contact.normal.x,
                    contact.normal.y,
                    cross(rB_rot, contact.normal)
                );

                // Tangent constraint
                const tangent = new Float2(-contact.normal.y, contact.normal.x);
                this.J[baseIdx + 1] = new Float3(
                    tangent.x,
                    tangent.y,
                    cross(rB_rot, tangent)
                );
            }

            // Hessian is zero for contact constraints
            this.H[baseIdx] = new Float3x3();
            this.H[baseIdx + 1] = new Float3x3();
        }
    }

    draw(ctx) {
        if (!ctx || !SHOW_CONTACTS) return;

        ctx.save();

        for (let i = 0; i < this.numContacts; i++) {
            const contact = this.contacts[i];

            // Get contact point in world space
            const pA = this.bodyA.position.xy().add(rotation(this.bodyA.position.z).mulVec(contact.rA));

            // Draw contact point
            ctx.fillStyle = contact.stick ? '#ff0000' : '#ffff00';
            ctx.beginPath();
            ctx.arc(pA.x, pA.y, 2, 0, 2 * Math.PI);
            ctx.fill();

            // Draw normal
            ctx.strokeStyle = '#ff0000';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(pA.x, pA.y);
            ctx.lineTo(pA.x + contact.normal.x * 10, pA.y + contact.normal.y * 10);
            ctx.stroke();
        }

        ctx.restore();
    }

    // Static collision detection function
    static collide(bodyA, bodyB, contacts) {
        // Simple box-box collision detection using SAT (Separating Axis Theorem)
        const posA = bodyA.position.xy();
        const posB = bodyB.position.xy();
        const rotA = rotation(bodyA.position.z);
        const rotB = rotation(bodyB.position.z);

        // Get box vertices in world space
        const vertsA = Manifold.getBoxVertices(posA, bodyA.size, rotA);
        const vertsB = Manifold.getBoxVertices(posB, bodyB.size, rotB);

        // Test separation along box A's axes
        let separation = Manifold.testSeparation(vertsA, vertsB, rotA.col(0));
        if (separation > COLLISION_MARGIN) return 0;

        separation = Manifold.testSeparation(vertsA, vertsB, rotA.col(1));
        if (separation > COLLISION_MARGIN) return 0;

        // Test separation along box B's axes
        separation = Manifold.testSeparation(vertsA, vertsB, rotB.col(0));
        if (separation > COLLISION_MARGIN) return 0;

        separation = Manifold.testSeparation(vertsA, vertsB, rotB.col(1));
        if (separation > COLLISION_MARGIN) return 0;

        // If we get here, boxes are colliding
        // For simplicity, generate a single contact point at the center
        const contact = {
            feature: { value: 0 },
            rA: new Float2(0, 0),
            rB: new Float2(0, 0),
            normal: posB.sub(posA),
            JAn: new Float3(),
            JBn: new Float3(),
            JAt: new Float3(),
            JBt: new Float3(),
            C0: new Float2(),
            stick: false
        };

        // Normalize contact normal
        const normalLength = length(contact.normal);
        if (normalLength > 1e-6) {
            contact.normal = contact.normal.div(normalLength);
        } else {
            contact.normal = new Float2(1, 0);
        }

        // Set contact points (simplified - at body centers)
        contact.rA = new Float2(0, 0);
        contact.rB = new Float2(0, 0);

        contacts[0] = contact;
        return 1;
    }

    // Helper function to get box vertices
    static getBoxVertices(center, size, rotation) {
        const halfSize = size.mul(0.5);
        const vertices = [
            rotation.mulVec(new Float2(-halfSize.x, -halfSize.y)).add(center),
            rotation.mulVec(new Float2(halfSize.x, -halfSize.y)).add(center),
            rotation.mulVec(new Float2(halfSize.x, halfSize.y)).add(center),
            rotation.mulVec(new Float2(-halfSize.x, halfSize.y)).add(center)
        ];
        return vertices;
    }

    // Helper function to test separation along an axis
    static testSeparation(vertsA, vertsB, axis) {
        let minA = Infinity, maxA = -Infinity;
        let minB = Infinity, maxB = -Infinity;

        // Project vertices of A onto axis
        for (const vert of vertsA) {
            const proj = dot(vert, axis);
            minA = Math.min(minA, proj);
            maxA = Math.max(maxA, proj);
        }

        // Project vertices of B onto axis
        for (const vert of vertsB) {
            const proj = dot(vert, axis);
            minB = Math.min(minB, proj);
            maxB = Math.max(maxB, proj);
        }

        // Return separation distance (positive if separated)
        return Math.max(minA - maxB, minB - maxA);
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        Force, Joint, Spring, IgnoreCollision, Manifold,
        MAX_ROWS, PENALTY_MIN, PENALTY_MAX, COLLISION_MARGIN, STICK_THRESH, SHOW_CONTACTS
    };
}
