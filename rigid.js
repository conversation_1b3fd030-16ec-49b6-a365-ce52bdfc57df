/**
 * Rigid Body implementation for AVBD 2D Physics Engine
 * Based on the C++ implementation from https://github.com/savant117/avbd-demo2d
 */

// Import math functions (assuming they're available globally or via import)
// In a browser environment, these would be loaded from math.js

class Rigid {
    constructor(solver, size, density, friction, position, velocity = new Float3(0, 0, 0)) {
        this.solver = solver;
        this.forces = null; // Linked list of forces acting on this body
        this.next = null;   // Next body in solver's linked list
        
        // Physical properties
        this.size = size.clone();
        this.friction = friction;
        this.radius = Math.sqrt(size.x * size.x + size.y * size.y) * 0.5;
        
        // Mass properties
        this.mass = density * size.x * size.y;
        this.moment = this.mass * (size.x * size.x + size.y * size.y) / 12.0;
        
        // State variables
        this.position = position.clone();
        this.initial = new Float3();
        this.inertial = new Float3();
        this.velocity = velocity.clone();
        this.prevVelocity = new Float3();
        
        // Add to solver's body list
        this.next = solver.bodies;
        solver.bodies = this;
    }

    destructor() {
        // Remove from solver's body list
        if (this.solver.bodies === this) {
            this.solver.bodies = this.next;
        } else {
            let current = this.solver.bodies;
            while (current && current.next !== this) {
                current = current.next;
            }
            if (current) {
                current.next = this.next;
            }
        }

        // Remove all forces acting on this body
        while (this.forces) {
            this.forces.disable();
        }
    }

    // Check if this body is constrained to another body
    constrainedTo(other) {
        for (let force = this.forces; force; force = (force.bodyA === this) ? force.nextA : force.nextB) {
            if ((force.bodyA === this && force.bodyB === other) || 
                (force.bodyA === other && force.bodyB === this)) {
                // Check if it's an IgnoreCollision force
                if (force.constructor.name !== 'IgnoreCollision') {
                    return true;
                }
            }
        }
        return false;
    }

    // Draw the rigid body (implemented in renderer)
    draw(ctx) {
        // This method is now handled by the renderer
        // Kept for compatibility
    }

    // Get world position of a local point
    getWorldPoint(localPoint) {
        return transform(this.position, localPoint);
    }

    // Get local position of a world point
    getLocalPoint(worldPoint) {
        const Rt = rotation(-this.position.z);
        return Rt.mulVec(worldPoint.sub(this.position.xy()));
    }

    // Check if a world point is inside this body
    containsPoint(worldPoint) {
        const local = this.getLocalPoint(worldPoint);
        return local.x >= -this.size.x * 0.5 && local.x <= this.size.x * 0.5 &&
               local.y >= -this.size.y * 0.5 && local.y <= this.size.y * 0.5;
    }

    // Get the velocity of a point on the body in world coordinates
    getPointVelocity(worldPoint) {
        const r = worldPoint.sub(this.position.xy());
        return this.velocity.xy().add(new Float2(-r.y * this.velocity.z, r.x * this.velocity.z));
    }

    // Apply an impulse at a world point
    applyImpulse(impulse, worldPoint) {
        if (this.mass <= 0) return; // Static body
        
        const r = worldPoint.sub(this.position.xy());
        this.velocity.addInPlace(new Float3(impulse.x / this.mass, impulse.y / this.mass, cross(r, impulse) / this.moment));
    }

    // Apply a force at a world point (for integration)
    applyForce(force, worldPoint, dt) {
        if (this.mass <= 0) return; // Static body
        
        const impulse = force.mul(dt);
        this.applyImpulse(impulse, worldPoint);
    }

    // Get kinetic energy
    getKineticEnergy() {
        if (this.mass <= 0) return 0;
        
        const linearKE = 0.5 * this.mass * lengthSq(this.velocity.xy());
        const angularKE = 0.5 * this.moment * this.velocity.z * this.velocity.z;
        return linearKE + angularKE;
    }

    // Set as static (infinite mass)
    setStatic() {
        this.mass = 0;
        this.moment = 0;
        this.velocity = new Float3(0, 0, 0);
    }

    // Set as dynamic with given mass properties
    setDynamic(density) {
        this.mass = density * this.size.x * this.size.y;
        this.moment = this.mass * (this.size.x * this.size.x + this.size.y * this.size.y) / 12.0;
    }

    // Get the axis-aligned bounding box
    getAABB() {
        // For simplicity, use a conservative AABB that encompasses the rotated box
        const halfDiag = this.radius;
        return {
            minX: this.position.x - halfDiag,
            minY: this.position.y - halfDiag,
            maxX: this.position.x + halfDiag,
            maxY: this.position.y + halfDiag
        };
    }

    // Update the body's radius based on size (called when size changes)
    updateRadius() {
        this.radius = Math.sqrt(this.size.x * this.size.x + this.size.y * this.size.y) * 0.5;
    }

    // Integrate position using current velocity
    integrate(dt) {
        if (this.mass <= 0) return; // Static body
        
        this.position.addInPlace(this.velocity.mul(dt));
    }

    // Reset to initial state
    reset(position, velocity = new Float3(0, 0, 0)) {
        this.position = position.clone();
        this.velocity = velocity.clone();
        this.prevVelocity = new Float3(0, 0, 0);
        this.initial = new Float3(0, 0, 0);
        this.inertial = new Float3(0, 0, 0);
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { Rigid };
}
