/**
 * AVBD Solver for 2D Physics Engine
 * Based on the C++ implementation from https://github.com/savant117/avbd-demo2d
 */

class Solver {
    constructor() {
        this.bodies = null;  // Linked list of rigid bodies
        this.forces = null;  // Linked list of forces/constraints
        
        // Solver parameters
        this.dt = 1.0 / 60.0;        // Timestep
        this.gravity = -10.0;        // Gravity
        this.iterations = 10;        // Solver iterations
        this.alpha = 0.99;           // Stabilization parameter
        this.beta = 100000.0;        // Penalty ramping parameter
        this.gamma = 0.99;           // Warmstarting decay parameter
        
        this.defaultParams();
    }

    destructor() {
        this.clear();
    }

    // Find which body is at the given point
    pick(at, local = { value: new Float2() }) {
        for (let body = this.bodies; body !== null; body = body.next) {
            const Rt = rotation(-body.position.z);
            const localPoint = Rt.mulVec(at.sub(body.position.xy()));
            
            if (localPoint.x >= -body.size.x * 0.5 && localPoint.x <= body.size.x * 0.5 &&
                localPoint.y >= -body.size.y * 0.5 && localPoint.y <= body.size.y * 0.5) {
                local.value = localPoint;
                return body;
            }
        }
        return null;
    }

    // Clear all bodies and forces
    clear() {
        while (this.forces) {
            this.forces.disable();
        }
        while (this.bodies) {
            this.bodies.destructor();
        }
    }

    // Reset to default parameters
    defaultParams() {
        this.dt = 1.0 / 60.0;
        this.gravity = -10.0;
        this.iterations = 10;
        this.beta = 100000.0;
        this.alpha = 0.99;
        this.gamma = 0.99;
    }

    // Main simulation step
    step() {
        // Broadphase collision detection (naive O(n^2))
        for (let bodyA = this.bodies; bodyA !== null; bodyA = bodyA.next) {
            for (let bodyB = bodyA.next; bodyB !== null; bodyB = bodyB.next) {
                // Skip if bodies are already constrained to each other
                if (bodyA.constrainedTo(bodyB)) continue;

                // Expanded AABB test to catch near-collisions
                const posA = bodyA.position.xy();
                const posB = bodyB.position.xy();
                const halfSizeA = bodyA.size.mul(0.5);
                const halfSizeB = bodyB.size.mul(0.5);

                const dx = Math.abs(posA.x - posB.x);
                const dy = Math.abs(posA.y - posB.y);

                const requiredX = halfSizeA.x + halfSizeB.x + 0.1; // Small margin
                const requiredY = halfSizeA.y + halfSizeB.y + 0.1; // Small margin

                if (dx < requiredX && dy < requiredY) {
                    new Manifold(this, bodyA, bodyB);
                }
            }
        }

        // Initialize and warmstart forces
        let force = this.forces;
        while (force !== null) {
            const nextForce = force.next;
            
            if (!force.initialize()) {
                // Force is inactive, remove it
                force.disable();
            } else {
                // Warmstart dual variables and penalty parameters (Eq. 19)
                for (let i = 0; i < force.rows(); i++) {
                    force.lambda[i] = force.lambda[i] * this.alpha * this.gamma;
                    force.penalty[i] = clamp(force.penalty[i] * this.gamma, PENALTY_MIN, PENALTY_MAX);
                    
                    // Don't let penalty exceed material stiffness for soft constraints
                    if (isFinite(force.stiffness[i])) {
                        force.penalty[i] = Math.min(force.penalty[i], force.stiffness[i]);
                    }
                }
            }
            force = nextForce;
        }

        // Initialize and warmstart bodies (primal variables)
        for (let body = this.bodies; body !== null; body = body.next) {
            // Clamp angular velocity to prevent instability
            body.velocity.z = clamp(body.velocity.z, -10.0, 10.0);

            // Compute inertial position (Eq 2)
            body.inertial = body.position.add(body.velocity.mul(this.dt));
            if (body.mass > 0) {
                body.inertial.addInPlace(new Float3(0, this.gravity, 0).mul(this.dt * this.dt));
            }

            // Adaptive warmstart
            const accel = body.velocity.sub(body.prevVelocity).div(this.dt);
            const accelExt = accel.y * sign(this.gravity);
            let accelWeight = clamp(accelExt / Math.abs(this.gravity), 0.0, 1.0);
            if (!isFinite(accelWeight)) accelWeight = 0.0;

            // Save initial position and compute warmstarted position
            body.initial = body.position.clone();
            body.position = body.position.add(body.velocity.mul(this.dt))
                .add(new Float3(0, this.gravity, 0).mul(accelWeight * this.dt * this.dt));
        }

        // Main solver loop
        for (let it = 0; it < this.iterations; it++) {
            // Primal update
            for (let body = this.bodies; body !== null; body = body.next) {
                if (body.mass <= 0) continue; // Skip static bodies

                // Initialize linear system (Eqs. 5, 6)
                const M = diagonal(body.mass, body.mass, body.moment);
                let lhs = M.div(this.dt * this.dt);
                let rhs = M.div(this.dt * this.dt).mulVec(body.position.sub(body.inertial));

                // Iterate over all forces acting on the body
                for (let force = body.forces; force !== null; 
                     force = (force.bodyA === body) ? force.nextA : force.nextB) {
                    
                    // Compute constraint and derivatives
                    force.computeConstraint(this.alpha);
                    force.computeDerivatives(body);

                    for (let i = 0; i < force.rows(); i++) {
                        // Use lambda as 0 if it's not a hard constraint
                        const lambda = isFinite(force.stiffness[i]) ? 0.0 : force.lambda[i];

                        // Compute clamped force magnitude (Sec 3.2)
                        const f = clamp(
                            force.penalty[i] * force.C[i] + lambda + force.motor[i],
                            force.fmin[i],
                            force.fmax[i]
                        );

                        // Compute diagonally lumped geometric stiffness (Sec 3.5)
                        const G = diagonal(
                            length3(force.H[i].col(0)),
                            length3(force.H[i].col(1)),
                            length3(force.H[i].col(2))
                        ).mul(Math.abs(f));

                        // Accumulate force (Eq. 13) and hessian (Eq. 17)
                        rhs.addInPlace(force.J[i].mul(f));
                        lhs.addInPlace(outer3(force.J[i], force.J[i].mul(force.penalty[i])).add(G));
                    }
                }

                // Solve SPD linear system and apply update (Eq. 4)
                body.position.subInPlace(solve(lhs, rhs));
            }

            // Dual update
            for (let force = this.forces; force !== null; force = force.next) {
                force.computeConstraint(this.alpha);

                for (let i = 0; i < force.rows(); i++) {
                    // Use lambda as 0 if it's not a hard constraint
                    const lambda = isFinite(force.stiffness[i]) ? 0.0 : force.lambda[i];

                    // Update lambda (Eq 11)
                    force.lambda[i] = clamp(
                        force.penalty[i] * force.C[i] + lambda,
                        force.fmin[i],
                        force.fmax[i]
                    );

                    // Disable force if fracture threshold exceeded
                    if (Math.abs(force.lambda[i]) >= force.fracture[i]) {
                        force.disable();
                        break;
                    }

                    // Update penalty parameter (Eq. 16)
                    if (force.lambda[i] > force.fmin[i] && force.lambda[i] < force.fmax[i]) {
                        const newPenalty = force.penalty[i] + this.beta * Math.abs(force.C[i]);
                        const maxPenalty = isFinite(force.stiffness[i]) ? 
                            force.stiffness[i] : PENALTY_MAX;
                        force.penalty[i] = Math.min(newPenalty, maxPenalty);
                    }
                }
            }
        }

        // Compute velocities using BDF1
        for (let body = this.bodies; body !== null; body = body.next) {
            body.prevVelocity = body.velocity.clone();
            if (body.mass > 0) {
                body.velocity = body.position.sub(body.initial).div(this.dt);
            }
        }
    }

    // Draw all bodies and forces
    draw(ctx) {
        if (!ctx) return;

        for (let body = this.bodies; body !== null; body = body.next) {
            body.draw(ctx);
        }

        for (let force = this.forces; force !== null; force = force.next) {
            force.draw(ctx);
        }
    }

    // Add a new rigid body
    addBody(size, density, friction, position, velocity = new Float3(0, 0, 0)) {
        return new Rigid(this, size, density, friction, position, velocity);
    }

    // Add a joint constraint
    addJoint(bodyA, bodyB, rA, rB, stiffness = new Float3(Infinity, Infinity, Infinity), motor = 0.0, fracture = Infinity) {
        return new Joint(this, bodyA, bodyB, rA, rB, stiffness, motor, fracture);
    }

    // Add a spring force
    addSpring(bodyA, bodyB, rA, rB, stiffness, rest = -1) {
        return new Spring(this, bodyA, bodyB, rA, rB, stiffness, rest);
    }

    // Add ignore collision constraint
    addIgnoreCollision(bodyA, bodyB) {
        return new IgnoreCollision(this, bodyA, bodyB);
    }

    // Get total kinetic energy of the system
    getTotalKineticEnergy() {
        let totalKE = 0;
        for (let body = this.bodies; body !== null; body = body.next) {
            totalKE += body.getKineticEnergy();
        }
        return totalKE;
    }

    // Get number of bodies
    getBodyCount() {
        let count = 0;
        for (let body = this.bodies; body !== null; body = body.next) {
            count++;
        }
        return count;
    }

    // Get number of constraints
    getConstraintCount() {
        let count = 0;
        for (let force = this.forces; force !== null; force = force.next) {
            count += force.rows();
        }
        return count;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { Solver };
}
