/**
 * Rendering System for AVBD 2D Physics Engine
 * Canvas-based visualization with camera controls
 */

class Renderer {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        
        // Camera properties
        this.camPos = new Float2(0, 5);
        this.camZoom = 25.0;
        
        // Rendering options
        this.showContacts = true;
        this.showConstraints = true;
        this.showVelocities = false;
        this.showAABB = false;
        
        // Colors
        this.colors = {
            background: '#ffffff',
            body: '#6496c8',
            bodyStroke: '#000000',
            staticBody: '#808080',
            contact: '#ffff00',
            stickContact: '#ff0000',
            joint: '#ff0000',
            spring: '#00ff00',
            velocity: '#0000ff',
            aabb: '#ff00ff'
        };
        
        this.setupCanvas();
    }

    setupCanvas() {
        // Get actual canvas size
        const rect = this.canvas.getBoundingClientRect();

        // Set canvas internal size to match display size
        this.canvas.width = rect.width;
        this.canvas.height = rect.height;

        // Set rendering quality
        this.ctx.imageSmoothingEnabled = true;
        this.ctx.lineCap = 'round';
        this.ctx.lineJoin = 'round';
    }

    // Convert world coordinates to screen coordinates
    worldToScreen(worldPos) {
        const screenX = (worldPos.x - this.camPos.x) * this.camZoom + this.canvas.width / 2;
        const screenY = (this.camPos.y - worldPos.y) * this.camZoom + this.canvas.height / 2;
        return new Float2(screenX, screenY);
    }

    // Convert screen coordinates to world coordinates
    screenToWorld(screenPos) {
        const worldX = this.camPos.x + (screenPos.x - this.canvas.width / 2) / this.camZoom;
        const worldY = this.camPos.y - (screenPos.y - this.canvas.height / 2) / this.camZoom;
        return new Float2(worldX, worldY);
    }

    // Set up camera transform
    setupCamera() {
        this.ctx.save();
        this.ctx.translate(this.canvas.width / 2, this.canvas.height / 2);
        this.ctx.scale(this.camZoom, -this.camZoom); // Flip Y axis
        this.ctx.translate(-this.camPos.x, -this.camPos.y);
    }

    // Restore camera transform
    restoreCamera() {
        this.ctx.restore();
    }

    // Clear the canvas
    clear() {
        this.ctx.fillStyle = this.colors.background;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }

    // Draw a rigid body
    drawBody(body) {
        this.ctx.save();
        
        // Transform to body's coordinate system
        this.ctx.translate(body.position.x, body.position.y);
        this.ctx.rotate(body.position.z);
        
        // Choose color based on body type
        const isStatic = body.mass <= 0;
        this.ctx.fillStyle = isStatic ? this.colors.staticBody : this.colors.body;
        this.ctx.strokeStyle = this.colors.bodyStroke;
        this.ctx.lineWidth = 2 / this.camZoom;
        
        // Draw the box
        const halfWidth = body.size.x * 0.5;
        const halfHeight = body.size.y * 0.5;
        
        this.ctx.fillRect(-halfWidth, -halfHeight, body.size.x, body.size.y);
        this.ctx.strokeRect(-halfWidth, -halfHeight, body.size.x, body.size.y);
        
        // Draw orientation indicator
        this.ctx.strokeStyle = '#ff0000';
        this.ctx.lineWidth = 1 / this.camZoom;
        this.ctx.beginPath();
        this.ctx.moveTo(0, 0);
        this.ctx.lineTo(halfWidth * 0.6, 0);
        this.ctx.stroke();
        
        this.ctx.restore();
        
        // Draw velocity vector if enabled
        if (this.showVelocities && body.mass > 0) {
            this.drawVelocity(body);
        }
        
        // Draw AABB if enabled
        if (this.showAABB) {
            this.drawAABB(body);
        }
    }

    // Draw velocity vector
    drawVelocity(body) {
        const vel = body.velocity.xy();
        if (lengthSq(vel) < 0.01) return; // Don't draw very small velocities
        
        this.ctx.save();
        this.ctx.strokeStyle = this.colors.velocity;
        this.ctx.lineWidth = 2 / this.camZoom;
        this.ctx.setLineDash([5 / this.camZoom, 5 / this.camZoom]);
        
        this.ctx.beginPath();
        this.ctx.moveTo(body.position.x, body.position.y);
        this.ctx.lineTo(body.position.x + vel.x, body.position.y + vel.y);
        this.ctx.stroke();
        
        this.ctx.restore();
    }

    // Draw AABB
    drawAABB(body) {
        const aabb = body.getAABB();
        
        this.ctx.save();
        this.ctx.strokeStyle = this.colors.aabb;
        this.ctx.lineWidth = 1 / this.camZoom;
        this.ctx.setLineDash([3 / this.camZoom, 3 / this.camZoom]);
        
        this.ctx.strokeRect(
            aabb.minX, aabb.minY,
            aabb.maxX - aabb.minX, aabb.maxY - aabb.minY
        );
        
        this.ctx.restore();
    }

    // Draw a joint constraint
    drawJoint(joint) {
        if (!this.showConstraints || !joint.bodyA || !joint.bodyB) return;
        
        const pA = joint.bodyA.position.xy().add(rotation(joint.bodyA.position.z).mulVec(joint.rA));
        const pB = joint.bodyB.position.xy().add(rotation(joint.bodyB.position.z).mulVec(joint.rB));
        
        this.ctx.save();
        this.ctx.fillStyle = this.colors.joint;
        this.ctx.strokeStyle = this.colors.joint;
        this.ctx.lineWidth = 2 / this.camZoom;
        
        // Draw connection points
        this.ctx.beginPath();
        this.ctx.arc(pA.x, pA.y, 3 / this.camZoom, 0, 2 * Math.PI);
        this.ctx.fill();
        
        this.ctx.beginPath();
        this.ctx.arc(pB.x, pB.y, 3 / this.camZoom, 0, 2 * Math.PI);
        this.ctx.fill();
        
        this.ctx.restore();
    }

    // Draw a spring constraint
    drawSpring(spring) {
        if (!this.showConstraints || !spring.bodyA || !spring.bodyB) return;
        
        const pA = spring.bodyA.position.xy().add(rotation(spring.bodyA.position.z).mulVec(spring.rA));
        const pB = spring.bodyB.position.xy().add(rotation(spring.bodyB.position.z).mulVec(spring.rB));
        
        this.ctx.save();
        this.ctx.strokeStyle = this.colors.spring;
        this.ctx.lineWidth = 1 / this.camZoom;
        this.ctx.setLineDash([5 / this.camZoom, 5 / this.camZoom]);
        
        this.ctx.beginPath();
        this.ctx.moveTo(pA.x, pA.y);
        this.ctx.lineTo(pB.x, pB.y);
        this.ctx.stroke();
        
        this.ctx.restore();
    }

    // Draw contact manifold
    drawManifold(manifold) {
        if (!this.showContacts) return;
        
        for (let i = 0; i < manifold.numContacts; i++) {
            const contact = manifold.contacts[i];
            
            // Get contact point in world space
            const pA = manifold.bodyA.position.xy().add(rotation(manifold.bodyA.position.z).mulVec(contact.rA));
            
            this.ctx.save();
            
            // Draw contact point
            this.ctx.fillStyle = contact.stick ? this.colors.stickContact : this.colors.contact;
            this.ctx.beginPath();
            this.ctx.arc(pA.x, pA.y, 2 / this.camZoom, 0, 2 * Math.PI);
            this.ctx.fill();
            
            // Draw normal
            this.ctx.strokeStyle = this.colors.stickContact;
            this.ctx.lineWidth = 1 / this.camZoom;
            this.ctx.beginPath();
            this.ctx.moveTo(pA.x, pA.y);
            this.ctx.lineTo(pA.x + contact.normal.x * 10 / this.camZoom, pA.y + contact.normal.y * 10 / this.camZoom);
            this.ctx.stroke();
            
            this.ctx.restore();
        }
    }

    // Draw grid
    drawGrid() {
        this.ctx.save();
        this.ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
        this.ctx.lineWidth = 1 / this.camZoom;
        
        const gridSize = 1.0;
        const startX = Math.floor((this.camPos.x - this.canvas.width / (2 * this.camZoom)) / gridSize) * gridSize;
        const endX = Math.ceil((this.camPos.x + this.canvas.width / (2 * this.camZoom)) / gridSize) * gridSize;
        const startY = Math.floor((this.camPos.y - this.canvas.height / (2 * this.camZoom)) / gridSize) * gridSize;
        const endY = Math.ceil((this.camPos.y + this.canvas.height / (2 * this.camZoom)) / gridSize) * gridSize;
        
        // Vertical lines
        for (let x = startX; x <= endX; x += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, startY);
            this.ctx.lineTo(x, endY);
            this.ctx.stroke();
        }
        
        // Horizontal lines
        for (let y = startY; y <= endY; y += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(startX, y);
            this.ctx.lineTo(endX, y);
            this.ctx.stroke();
        }
        
        this.ctx.restore();
    }

    // Main render function
    render(solver) {
        this.clear();
        this.setupCamera();
        
        // Draw grid
        this.drawGrid();
        
        // Draw all bodies
        for (let body = solver.bodies; body !== null; body = body.next) {
            this.drawBody(body);
        }
        
        // Draw all constraints
        for (let force = solver.forces; force !== null; force = force.next) {
            if (force.constructor.name === 'Joint') {
                this.drawJoint(force);
            } else if (force.constructor.name === 'Spring') {
                this.drawSpring(force);
            } else if (force.constructor.name === 'Manifold') {
                this.drawManifold(force);
            }
        }
        
        this.restoreCamera();
    }

    // Camera controls
    moveCamera(dx, dy) {
        this.camPos.x += dx / this.camZoom;
        this.camPos.y += dy / this.camZoom;
    }

    zoomCamera(factor) {
        this.camZoom *= factor;
        this.camZoom = clamp(this.camZoom, 1.0, 1000.0);
    }

    setCameraPosition(x, y) {
        this.camPos.x = x;
        this.camPos.y = y;
    }

    setCameraZoom(zoom) {
        this.camZoom = clamp(zoom, 1.0, 1000.0);
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { Renderer };
}
