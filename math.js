/**
 * JavaScript implementation of the math library for AVBD 2D Physics Engine
 * Based on the C++ implementation from https://github.com/savant117/avbd-demo2d
 */

// Math types
class Float2 {
    constructor(x = 0, y = 0) {
        this.x = x;
        this.y = y;
    }

    // Array-like access
    get(i) {
        return i === 0 ? this.x : this.y;
    }

    set(i, value) {
        if (i === 0) this.x = value;
        else this.y = value;
    }

    // Operators
    add(other) {
        return new Float2(this.x + other.x, this.y + other.y);
    }

    sub(other) {
        return new Float2(this.x - other.x, this.y - other.y);
    }

    mul(scalar) {
        return new Float2(this.x * scalar, this.y * scalar);
    }

    div(scalar) {
        return new Float2(this.x / scalar, this.y / scalar);
    }

    neg() {
        return new Float2(-this.x, -this.y);
    }

    addInPlace(other) {
        this.x += other.x;
        this.y += other.y;
        return this;
    }

    subInPlace(other) {
        this.x -= other.x;
        this.y -= other.y;
        return this;
    }

    clone() {
        return new Float2(this.x, this.y);
    }
}

class Float3 {
    constructor(x = 0, y = 0, z = 0) {
        this.x = x;
        this.y = y;
        this.z = z;
    }

    // Get xy as Float2
    xy() {
        return new Float2(this.x, this.y);
    }

    // Array-like access
    get(i) {
        return i === 0 ? this.x : i === 1 ? this.y : this.z;
    }

    set(i, value) {
        if (i === 0) this.x = value;
        else if (i === 1) this.y = value;
        else this.z = value;
    }

    // Operators
    add(other) {
        return new Float3(this.x + other.x, this.y + other.y, this.z + other.z);
    }

    sub(other) {
        return new Float3(this.x - other.x, this.y - other.y, this.z - other.z);
    }

    mul(scalar) {
        return new Float3(this.x * scalar, this.y * scalar, this.z * scalar);
    }

    div(scalar) {
        return new Float3(this.x / scalar, this.y / scalar, this.z / scalar);
    }

    neg() {
        return new Float3(-this.x, -this.y, -this.z);
    }

    addInPlace(other) {
        this.x += other.x;
        this.y += other.y;
        this.z += other.z;
        return this;
    }

    subInPlace(other) {
        this.x -= other.x;
        this.y -= other.y;
        this.z -= other.z;
        return this;
    }

    clone() {
        return new Float3(this.x, this.y, this.z);
    }
}

class Float2x2 {
    constructor(row0 = new Float2(), row1 = new Float2()) {
        this.row = [row0, row1];
    }

    // Array-like access
    get(i) {
        return this.row[i];
    }

    col(i) {
        return new Float2(this.row[0].get(i), this.row[1].get(i));
    }

    // Matrix multiplication with Float2
    mulVec(vec) {
        return new Float2(
            dot(this.row[0], vec),
            dot(this.row[1], vec)
        );
    }

    // Matrix operations
    add(other) {
        return new Float2x2(
            this.row[0].add(other.row[0]),
            this.row[1].add(other.row[1])
        );
    }

    sub(other) {
        return new Float2x2(
            this.row[0].sub(other.row[0]),
            this.row[1].sub(other.row[1])
        );
    }

    mul(scalar) {
        return new Float2x2(
            this.row[0].mul(scalar),
            this.row[1].mul(scalar)
        );
    }

    div(scalar) {
        return new Float2x2(
            this.row[0].div(scalar),
            this.row[1].div(scalar)
        );
    }

    // Matrix multiplication
    mulMat(other) {
        return new Float2x2(
            new Float2(dot(this.row[0], other.col(0)), dot(this.row[0], other.col(1))),
            new Float2(dot(this.row[1], other.col(0)), dot(this.row[1], other.col(1)))
        );
    }
}

class Float3x3 {
    constructor(row0 = new Float3(), row1 = new Float3(), row2 = new Float3()) {
        this.row = [row0, row1, row2];
    }

    // Array-like access
    get(i) {
        return this.row[i];
    }

    col(i) {
        return new Float3(this.row[0].get(i), this.row[1].get(i), this.row[2].get(i));
    }

    // Matrix multiplication with Float3
    mulVec(vec) {
        return new Float3(
            dot3(this.row[0], vec),
            dot3(this.row[1], vec),
            dot3(this.row[2], vec)
        );
    }

    // Matrix operations
    add(other) {
        return new Float3x3(
            this.row[0].add(other.row[0]),
            this.row[1].add(other.row[1]),
            this.row[2].add(other.row[2])
        );
    }

    addInPlace(other) {
        this.row[0].addInPlace(other.row[0]);
        this.row[1].addInPlace(other.row[1]);
        this.row[2].addInPlace(other.row[2]);
        return this;
    }

    sub(other) {
        return new Float3x3(
            this.row[0].sub(other.row[0]),
            this.row[1].sub(other.row[1]),
            this.row[2].sub(other.row[2])
        );
    }

    mul(scalar) {
        return new Float3x3(
            this.row[0].mul(scalar),
            this.row[1].mul(scalar),
            this.row[2].mul(scalar)
        );
    }

    div(scalar) {
        return new Float3x3(
            this.row[0].div(scalar),
            this.row[1].div(scalar),
            this.row[2].div(scalar)
        );
    }
}

// Math functions
function sign(x) {
    return x < 0 ? -1 : x > 0 ? 1 : 0;
}

function min(a, b) {
    return a < b ? a : b;
}

function max(a, b) {
    return a > b ? a : b;
}

function clamp(x, a, b) {
    return max(a, min(b, x));
}

function dot(a, b) {
    return a.x * b.x + a.y * b.y;
}

function dot3(a, b) {
    return a.x * b.x + a.y * b.y + a.z * b.z;
}

function lengthSq(v) {
    return dot(v, v);
}

function length(v) {
    return Math.sqrt(dot(v, v));
}

function lengthSq3(v) {
    return dot3(v, v);
}

function length3(v) {
    return Math.sqrt(dot3(v, v));
}

function cross(a, b) {
    return a.x * b.y - a.y * b.x;
}

function outer(a, b) {
    return new Float2x2(
        b.mul(a.x),
        b.mul(a.y)
    );
}

function outer3(a, b) {
    return new Float3x3(
        b.mul(a.x),
        b.mul(a.y),
        b.mul(a.z)
    );
}

function abs2(v) {
    return new Float2(Math.abs(v.x), Math.abs(v.y));
}

function abs2x2(a) {
    return new Float2x2(abs2(a.row[0]), abs2(a.row[1]));
}

function transpose(a) {
    return new Float2x2(
        new Float2(a.row[0].x, a.row[1].x),
        new Float2(a.row[0].y, a.row[1].y)
    );
}

function rotation(angle) {
    const c = Math.cos(angle);
    const s = Math.sin(angle);
    return new Float2x2(
        new Float2(c, -s),
        new Float2(s, c)
    );
}

function diagonal(m00, m11, m22) {
    return new Float3x3(
        new Float3(m00, 0, 0),
        new Float3(0, m11, 0),
        new Float3(0, 0, m22)
    );
}

function transform(q, v) {
    return rotation(q.z).mulVec(v).add(q.xy());
}

function rotate(angle, v) {
    return rotation(angle).mulVec(v);
}

// LDL^T solver for 3x3 SPD matrices
function solve(a, b) {
    // Compute LDL^T decomposition
    const D1 = a.row[0].x;
    const L21 = a.row[1].x / a.row[0].x;
    const L31 = a.row[2].x / a.row[0].x;
    const D2 = a.row[1].y - L21 * L21 * D1;
    const L32 = (a.row[2].y - L21 * L31 * D1) / D2;
    const D3 = a.row[2].z - (L31 * L31 * D1 + L32 * L32 * D2);

    // Forward substitution: Solve Ly = b
    const y1 = b.x;
    const y2 = b.y - L21 * y1;
    const y3 = b.z - L31 * y1 - L32 * y2;

    // Diagonal solve: Solve Dz = y
    const z1 = y1 / D1;
    const z2 = y2 / D2;
    const z3 = y3 / D3;

    // Backward substitution: Solve L^T x = z
    const x = new Float3();
    x.z = z3;
    x.y = z2 - L32 * x.z;
    x.x = z1 - L21 * x.y - L31 * x.z;

    return x;
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        Float2, Float3, Float2x2, Float3x3,
        sign, min, max, clamp, dot, dot3, lengthSq, length, lengthSq3, length3,
        cross, outer, outer3, abs2, abs2x2, transpose, rotation, diagonal,
        transform, rotate, solve
    };
}
